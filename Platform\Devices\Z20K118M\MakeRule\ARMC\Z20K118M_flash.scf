#! armclang --target=arm-arm-none-eabi -mcpu=cortex-m0plus -xc -E
; *************************************************************
; *** Scatter-Loading Description File                      ***
; *************************************************************

#define pflash_start               0x00000000
#define pflash_size                0x00040000    /* 256KB */

#define dflash_start               0x01000000
#define dflash_size                0x00020000    /* 128KB */


#define ram0_start                 0x1fffc000
#define ram0_size                  0x00004000    /* 16KB */
#define ram0_end                   ram0_start + ram0_size

#define ram1_start                 0x20000000
#define ram1_size                  0x00004000    /* 16KB */
#define ram1_end                   ram1_start + ram1_size

LR_IROM1 pflash_start pflash_size  {    ; load region size_region
  ER_IROM1 pflash_start pflash_size  {  ; load address = execution address
   *.o (.intvec, +First)
   *(InRoot$$Sections)
   .ANY (+RO)
  }
  
  RAM_START ram0_start ram0_size  {  ; code execution in ram
   .ANY (.code_ram)
  }
  
  RW_IRAM1 ram1_start ram1_size  {  ; RW data
   .ANY (+RW +ZI)
  }  

  ARM_LIB_STACK ram1_end EMPTY -0x400     {}; Stack region growing down
  ARM_LIB_HEAP	ImageBase(ARM_LIB_STACK) EMPTY -0x200	 {} ; heap region
}

