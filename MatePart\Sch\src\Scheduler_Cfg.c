#include "Scheduler.h"
#include "Scheduler_Cfg.h"
#define DEV_SER         0x40u
#define DEV_DES         0x48u




uint32_t u32Task1value      = 0u;
uint32_t u32Task2value      = 0u;
uint32_t u32Task3value      = 0u;
uint32_t u32Task4value      = 0u;
uint32_t u32Task5value      = 0u;
uint32_t u32Task6value      = 0u;
uint32_t u32Task7value      = 0u;
uint32_t u32Task8value      = 0u;
uint32_t u32Task9value      = 0u;
uint32_t u32Task10value     = 0u;
uint32_t u32Task11value     = 0u;
uint32_t u32Task12value     = 0u;
uint32_t u32Task13value     = 0u;
bool     GpioFlag = true;

void Task1(void)
{

}

void Task2(void)
{
	
}

void Task3(void)
{
	
}

void Task4(void)
{
	
}

void Task5(void)
{
	
}

void Task6(void)
{
	
}

void Task7(void)
{
	
}

void Task8(void)
{
	
}

void Task9(void)
{

}

void Task10(void)
{

}

extern void GPIOIntInit(void);

extern uint8_t u8RecvDuration;

// update timer
static uint16_t u16UpdateTimer = 0;
bool boTimer_200MsFlag = false;
bool boTimer_2MsFlag = false;
bool boTimer_100MsFlag = false;
bool boTimer_5MsFlag = false; 

uint8_t u8InitFlag = 0u;
uint8_t u8InitFlagSer = 0u;

void Task11(void)           //1msTask
{
    // uint8_t au8RegData[8u] = {1u, 0u, 0x32u, 0u, 0u, 0u, 0u, 0u};
    // static uint16_t u16Index = 0u;
    // static uint16_t u16TimeWait5S = 3u;

    // if(u8RecvDuration)          //串口计数用的
    // {
    //     u8RecvDuration -= 1u;
    // }

	// u32Task11value ++;
	// if(u32Task11value >= 10u)
	// {
	// 	u32Task11value = 0u;
    //     /* SER init */
    //     // if(GPIO_ReadPinLevel(PORT_C , GPIO_5) == GPIO_LOW)
    //     // {
    //     //     u16Index = 0u;
    //     //     u16TimeWait5S = 50;
    //     //     if(u8InitFlag || u8InitFlagSer)
    //     //     {
    //     //         NVIC_SystemReset();
    //     //     }
    //     // }
    //     // else if(GPIO_ReadPinLevel(PORT_C , GPIO_5) == GPIO_HIGH && u16TimeWait5S)
    //     // {
            
    //     //     u16TimeWait5S --;
    //     //     if(!u16TimeWait5S)
    //     //     {
    //     //         I2C_Disable(I2C0_ID);
    //     //         I2C_SetTargetAddr(I2C0_ID, DEV_SER);
    //     //         I2C_Enable(I2C0_ID);
    //     //     }
    //     // }
    //     // else if(!u8InitFlag)
    //     // {
    //     //     u8InitFlagSer = 1;
    //     //     if (u16Index < 131)
    //     //     {
    //     //         Ex_MstWriteBuffer(I2C0_ID, (uint8_t *)&au8SerHdmi[u16Index].u16DstRegAddr, (uint8_t *)&au8SerHdmi[u16Index].u8RegData);
    //     //         u16Index += 1u;
    //     //     }
    //     //     else if(u16Index >= 131)
    //     //     {
    //     //         /* SER init finished, start to init DES */
    //     //         u16Index = 0u;
    //     //         u8InitFlag = 1u;
    //     //         I2C_Disable(I2C0_ID);
    //     //         I2C_SetTargetAddr(I2C0_ID, DEV_DES);
    //     //         I2C_Enable(I2C0_ID);
    //     //     }
    //     //     else
    //     //     {
    //     //         /* Do nothing */
    //     //     }
    //     // }
    //     // /* Des init */
    //     // else if(u8InitFlag == 1u)
    //     // {
    //     //     if(u16Index < 8u)
    //     //     {
    //     //         Ex_MstWriteBuffer(I2C0_ID, (uint8_t*)&au8DesHdmi[u16Index].u16DstRegAddr, (uint8_t*)&au8DesHdmi[u16Index].u8RegData);
    //     //         u16Index += 1u;
    //     //     }
    //     //     else if(u16Index >= 8u)
    //     //     {
    //     //         /* Des init finished, start to read SER reg */
    //     //         u16Index = 0u;
    //     //         u8InitFlag = 2u;
    //     //         I2C_Disable(I2C1_ID);
    //     //         I2C_SetTargetAddr(I2C1_ID, MCU_IIC);
    //     //         I2C_Enable(I2C1_ID);
    //     //     }
    //     //     else
    //     //     {
    //     //         /* Do nothing */
    //     //     }
    //     // }
    //     // else if(u8InitFlag == 2u)
    //     // {
    //     //     u8InitFlag = 3u;
    //     //     au8RegData[7u] = au8RegData[0u]^au8RegData[1u]^au8RegData[2u]^au8RegData[3u]^au8RegData[4u]^au8RegData[5u]^au8RegData[6u];
    //     //     Ex_MstWriteArray(I2C1_ID, (uint8_t*)&au8RegData[0u]);
    //     // }
    //     // else if(u8InitFlag == 3u)
    //     // {
    //     //     u8InitFlag = 4u;
    //     //     GPIOIntInit();
    //     // }
    //     // else if(u8InitFlag == 4u)
    //     // {
    //     //     u8InitFlag = 5u;
    //     //     u8StartEOLTest = 1;
    //     // }
    //     // else
    //     // {

    //     // }

    //     if(GpioFlag)
    //     {
    //         GPIOIntInit();
    //         GpioFlag=false;
    //     }
        
    //     u8InitFlag = 5u;
    //     App_vMain();
	// }

    // //升级的计时器
    // if(boTimer_200ms)
    // {
    //     u16UpdateTimer++;
    //     if(u16UpdateTimer > 200)
    //     {
    //         u16UpdateTimer = 0;
    //         boTimer_200ms = false;
    //         boTimer_200MsFlag = true;
    //     }
    // }
    // else if(boTimer_2ms)
    // {
    //     u16UpdateTimer++;
    //     if(u16UpdateTimer > 2)
    //     {
    //         u16UpdateTimer = 0;
    //         boTimer_2ms = false;
    //         boTimer_2MsFlag = true;
    //     }
    // }
    // else if(boTimer_100ms)
    // {
    //     u16UpdateTimer++;
    //     if(u16UpdateTimer > 100)
    //     {
    //         u16UpdateTimer = 0;
    //         boTimer_100ms = false;
    //         boTimer_100MsFlag = true;
    //     }
    // }
    // else if(boTimer_5ms)
    // {
    //     u16UpdateTimer++;
    //     if(u16UpdateTimer > 50)
    //     {
    //         u16UpdateTimer = 0;
    //         boTimer_5ms = false;
    //         boTimer_5MsFlag = true;
    //     }
    // }

    // // if(u8UartUpdateState == Updating)
    // // {
    // //     App_vUpdateFraneSend();
    // // }

}

void Task12(void)
{
    
}

void Task13(void)
{

}

const Sch_Task_Info_T Sch_RunTaskTable[] =
{
	/* always online tasks: ------------------------------------------*/
	{&Task12,              SCH_MS( 0u),            SCH_TIMEDURATION( 0u)},
	{SCH_EndOfList,        SCH_MS( 1u),            SCH_TIMEDURATION( 0u)},
	/* 1ms periodic tasks: -------------------------------------------*/
	{&Task11,              SCH_MS( 0u),            SCH_TIMEDURATION( 1u)},
	{SCH_EndOfList,        SCH_MS( 1u),            SCH_TIMEDURATION( 1u)},
	/* 10ms periodic tasks: -------------------------------------------*/
	{&Task1,               SCH_MS( 0u),            SCH_TIMEDURATION( 1u)},
	{&Task2,               SCH_MS( 1u),            SCH_TIMEDURATION( 2u)},
	{&Task3,               SCH_MS( 2u),            SCH_TIMEDURATION( 3u)},
	{&Task4,               SCH_MS( 3u),            SCH_TIMEDURATION( 4u)},
	{&Task5,               SCH_MS( 4u),            SCH_TIMEDURATION( 5u)},
	{&Task6,               SCH_MS( 5u),            SCH_TIMEDURATION( 5u)},
	{&Task7,               SCH_MS( 6u),            SCH_TIMEDURATION( 7u)},
	{&Task8,               SCH_MS( 7u),            SCH_TIMEDURATION( 8u)},
	{&Task9,               SCH_MS( 8u),            SCH_TIMEDURATION( 9u)},
	{&Task10,              SCH_MS( 9u),            SCH_TIMEDURATION(10u)},
	{SCH_EndOfList,        SCH_MS(10u),            SCH_TIMEDURATION(10u)},
    /* 20ms periodic tasks: -------------------------------------------*/
	{&Task13,              SCH_MS( 0u),            SCH_TIMEDURATION(20u)},
	{SCH_EndOfList,        SCH_MS(20u),            SCH_TIMEDURATION(20u)},
	{SCH_EndOfList,        SCH_MS( 0u),            SCH_TIMEDURATION( 0u)}
};
