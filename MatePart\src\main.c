/**************************************************************************************************/
/**
 * @file      : main.c
 * @brief     : 
 * @version   : V1.8.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @note      : This example contains sample code for customer evaluation purpose only. It is not
 * part of the production code deliverables. The example code is only tested under defined
 * environment with related context of the whole example project. Therefore, it is not guaranteed
 * that the example always works under user environment due to the diversity of hardware and
 * software environment. Do not use pieces copy of the example code without prior and separate
 * verification and validation in user environment.
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "Z20K11xM_drv.h"
#include "Z20K11xM_clock.h"
#include "Z20K11xM_sysctrl.h"
#include "Z20K11xM_wdog.h"
#include "Z20K11xM_gpio.h"
#include "Z20K11xM_uart.h"
#include "Z20K118M.h" 
#include "common_func.h"
#include "SysTick.h"
#include "Scheduler.h"
#include "can.h"
#include "factory_mode.h"
#include "security_access.h"
#include "display_control.h"
#include "button_handler.h"

// #define WDOG_EN		0



__STATIC_INLINE void SysTick_Init(void)
{
  SysTick->LOAD  = 0x00FFFFFEu;                         /* set reload register */
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
}

uint8_t u8SW2 = 0u;
uint8_t u8SW1 = 0u;
uint8_t u8SW3 = 0u;


void System_Init(void)
{
    /* Disable wdog */
    SYSCTRL_EnableModule(SYSCTRL_WDOG);
    WDOG_Disable();

    /* Enable OSC40M clock*/
    CLK_OSC40MEnable2(CLK_OSC_FREQ_MODE_HIGH, ENABLE, CLK_OSC_XTAL);
    /* Select OSC40M as system clock*/
    CLK_SysClkSrc(CLK_SYS_FIRC64M);         // zx review , CLK_SYS_OSC40M -> CLK_SYS_FIRC64M
    CLK_SetClkDivider(CLK_CORE, CLK_DIV_1); // zx review
    CLK_SetClkDivider(CLK_BUS, CLK_DIV_2);  // zx review
    CLK_SetClkDivider(CLK_SLOW, CLK_DIV_8); // zx review
}

void Common_Init(void)
{
    CLK_ModuleSrc(CLK_PORTA, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTA);

    CLK_ModuleSrc(CLK_PORTB, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTB);

    CLK_ModuleSrc(CLK_PORTC, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTC);

    CLK_ModuleSrc(CLK_PORTD, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTD);

    CLK_ModuleSrc(CLK_PORTE, CLK_SRC_OSC40M);
    SYSCTRL_EnableModule(SYSCTRL_PORTE);

    /* enable GPIO module*/
    SYSCTRL_EnableModule(SYSCTRL_GPIO);
}

void Peripheral_Init(void)
{
    // I2c_Init();
    // Uart0_Init();
    SysTickInit();
    CANConfig_Init();    //v1.0.3
}

void Module_Init(void)
{
    /* Initialize all modules */
    Factory_Mode_Init();
    Security_Access_Init();
    Display_Control_Init();
    Button_Handler_Init();
}

int main()
{
    /* System initialization */
    System_Init();
    /* Common module initialization */
    Common_Init();

    /* Pull up deserializer power v1.0.1 */
    /* NOTE: PTA11 and PTA10 are reserved for CAN TX/RX - DO NOT configure as GPIO */
    // PORT_PinmuxConfig(PORT_A, GPIO_11, PTA11_GPIO);  // RESERVED FOR CAN TX
    // GPIO_SetPinDir(PORT_A, GPIO_11, GPIO_OUTPUT);
    // GPIO_SetPinOutput(PORT_A, GPIO_11);

    /* Pull up deserializer power */
    PORT_PinmuxConfig(PORT_A, GPIO_13, PTA13_GPIO);
    GPIO_SetPinDir(PORT_A, GPIO_13, GPIO_OUTPUT);
    GPIO_SetPinOutput(PORT_A, GPIO_13);

    PORT_PinmuxConfig(PORT_A, GPIO_12, PTA12_GPIO);
    GPIO_SetPinDir(PORT_A, GPIO_12, GPIO_OUTPUT);
    GPIO_SetPinOutput(PORT_A, GPIO_12);

    /* Set pin as GPIO */
    PORT_PinmuxConfig(PORT_C, GPIO_5, PTC5_GPIO);
    /* An initial voltage */
    PORT_PullConfig(PORT_C, GPIO_5, PORT_PULL_DOWN);
    /* Input direction for PTC5 */
    GPIO_SetPinDir(PORT_C, GPIO_5, GPIO_INPUT);

    /* Initialize GPIO interrupts for buttons */
    GPIOIntInit();

    /* Initialize peripherals */
    Peripheral_Init();

    /* Initialize application modules */
    Module_Init();

    /* Wait for CAN initialization to complete */
    for(volatile uint32_t i = 0; i < 1000000; i++);

    /* 3 second delay after power-on, then start security access automatically */
    for(volatile uint32_t i = 0; i < 3000000; i++);  /* 3s delay */

    Enter_Security_Mode();

//    uint8_t can_data[64] = {0xAA,0xBB,0xCC,0xDD,0xEE};
//    CAN_Send_Button1_Msg(0x5F0, can_data);

    while(1)
    {
        if(Get_Button_State() != BUTTON_NONE)
        {
            Process_Button_CAN();
        }
    }
}
