#include <stdio.h>
#include <stdint.h>
#include "src/display_control.h"

void print_state(const char* description) {
    printf("\n=== %s ===\n", description);
    printf("Interface Mode: %d\n", Display_Get_Interface_Mode());
    printf("Gear Position: %d\n", Display_Get_Gear_Position());
    printf("Vehicle Speed: %d\n", Display_Get_Vehicle_Speed());
    printf("Door Status: %d\n", Display_Get_Door_Status());
    printf("Drive Mode: %d\n", Display_Get_Drive_Mode());
    
    uint8_t can_data[64];
    Build_CAN_Data(can_data);
    printf("CAN Data - Gear: %d, Speed: %d\n", can_data[2], can_data[3]);
}

void test_speed_logic() {
    printf("\n\n========== Testing Speed Logic ==========\n");
    
    // Initialize
    Display_Control_Init();
    
    // Set to D gear for speed testing
    Display_Set_Gear_Position(1); // D gear
    Display_Set_Vehicle_Speed(0);
    print_state("Initial state (D gear, speed 0)");
    
    // Test 0-10 range (increment by 1)
    printf("\n--- Testing 0-10 range (increment by 1) ---\n");
    for(int i = 0; i < 12; i++) {
        Display_Inc_Vehicle_Speed();
        printf("After increment %d: Speed = %d\n", i+1, Display_Get_Vehicle_Speed());
    }
    
    // Test 10-250 range (increment by 10)
    printf("\n--- Testing 10-250 range (increment by 10) ---\n");
    Display_Set_Vehicle_Speed(15);
    for(int i = 0; i < 5; i++) {
        Display_Inc_Vehicle_Speed();
        printf("After increment: Speed = %d\n", Display_Get_Vehicle_Speed());
    }
    
    // Test 250-255 jump
    printf("\n--- Testing 250-255 jump ---\n");
    Display_Set_Vehicle_Speed(250);
    printf("Before increment: Speed = %d\n", Display_Get_Vehicle_Speed());
    Display_Inc_Vehicle_Speed();
    printf("After increment: Speed = %d\n", Display_Get_Vehicle_Speed());
    
    // Test decrement from 255
    printf("\n--- Testing decrement from 255 ---\n");
    printf("Before decrement: Speed = %d\n", Display_Get_Vehicle_Speed());
    Display_Dec_Vehicle_Speed();
    printf("After decrement: Speed = %d\n", Display_Get_Vehicle_Speed());
    
    // Test decrement in 10-250 range
    printf("\n--- Testing decrement in 10-250 range ---\n");
    for(int i = 0; i < 3; i++) {
        Display_Dec_Vehicle_Speed();
        printf("After decrement: Speed = %d\n", Display_Get_Vehicle_Speed());
    }
    
    // Test decrement in 1-10 range
    printf("\n--- Testing decrement in 1-10 range ---\n");
    Display_Set_Vehicle_Speed(8);
    for(int i = 0; i < 10; i++) {
        printf("Before decrement: Speed = %d\n", Display_Get_Vehicle_Speed());
        Display_Dec_Vehicle_Speed();
        printf("After decrement: Speed = %d\n", Display_Get_Vehicle_Speed());
    }
}

void test_interface_switching() {
    printf("\n\n========== Testing Interface Switching ==========\n");
    
    // Initialize and set some values
    Display_Control_Init();
    Display_Set_Gear_Position(1); // D gear
    Display_Set_Vehicle_Speed(50);
    Display_Set_Drive_Mode(3); // Sport mode
    print_state("Initial state (interface 1, D gear, speed 50, sport mode)");
    
    // Switch to color interface
    printf("\n--- Switching to color interface (2) ---\n");
    Display_Set_Interface_Mode(2);
    print_state("After switching to interface 2");
    
    // Try to change speed in color interface (should not affect backup)
    printf("\n--- Trying to change speed in color interface ---\n");
    Display_Inc_Vehicle_Speed();
    Display_Inc_Vehicle_Speed();
    print_state("After trying to change speed in interface 2");
    
    // Switch to another color interface
    printf("\n--- Switching to interface 5 ---\n");
    Display_Set_Interface_Mode(5);
    print_state("After switching to interface 5");
    
    // Switch back to default interface
    printf("\n--- Switching back to default interface (1) ---\n");
    Display_Set_Interface_Mode(1);
    print_state("After switching back to interface 1");
}

int main() {
    printf("Testing Display Control Module\n");
    printf("==============================\n");
    
    test_speed_logic();
    test_interface_switching();
    
    printf("\n\nTest completed!\n");
    return 0;
}
