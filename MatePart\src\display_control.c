/**************************************************************************************************/
/**
 * @file      : display_control.c
 * @brief     : Display control and CAN data management module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "display_control.h"

/* Display state variable */
static DisplayState_t display_state = {
    .interface_mode = 1,    /* Default dashboard theme */
    .gear_position = 2,     /* Default P gear */
    .vehicle_speed = 0,     /* Default speed 0 */
    .door_status = 2,       /* Default 6 doors closed */
    .drive_mode = 1         /* Default comfort mode */
};

/* Current operation mode */
static DisplayMode_t current_mode = MODE_INTERFACE;

/**
 * @brief Initialize display control module
 */
void Display_Control_Init(void)
{
    /* Reset to default values */
    display_state.interface_mode = 1;
    display_state.gear_position = 2;
    display_state.vehicle_speed = 0;
    display_state.door_status = 2;
    display_state.drive_mode = 1;
    current_mode = MODE_INTERFACE;
}

/**
 * @brief Build CAN data from current display state
 * @param can_data: Pointer to CAN data buffer (64 bytes)
 */
void Build_CAN_Data(uint8_t *can_data)
{
    /* Clear buffer */
    for(int i = 0; i < 64; i++) {
        can_data[i] = 0x00;
    }
    
    /* Byte0: Main function (fixed value 0x0D) */
    can_data[0] = 0x0D;

    /* Byte1: Interface switching (1-6) */
    can_data[1] = display_state.interface_mode;

    /* Byte2: Gear display (1-4: D,P,N,R) */
    can_data[2] = display_state.gear_position;

    /* Byte3: Speed display (0-255, no speed in P gear) */
    if(display_state.gear_position == 2) { /* P gear */
        can_data[3] = 0;
    } else {
        can_data[3] = display_state.vehicle_speed;
    }

    /* Byte4: Door status (1-2: 6 doors open/closed) */
    can_data[4] = display_state.door_status;

    /* Byte5: Drive mode (1-4: comfort,drive,sport,eco) */
    can_data[5] = display_state.drive_mode;
}

/**
 * @brief Get current display mode
 * @return Current display mode
 */
DisplayMode_t Get_Current_Mode(void)
{
    return current_mode;
}

/**
 * @brief Set current display mode
 * @param mode: Display mode to set
 */
void Set_Current_Mode(DisplayMode_t mode)
{
    if(mode < MODE_MAX) {
        current_mode = mode;
    }
}

/**
 * @brief Switch to next mode
 */
void Next_Mode(void)
{
    current_mode++;
    if(current_mode >= MODE_MAX) {
        current_mode = MODE_INTERFACE;
    }
}

/* Display state getters */
uint8_t Display_Get_Interface_Mode(void) { return display_state.interface_mode; }
uint8_t Display_Get_Gear_Position(void) { return display_state.gear_position; }
uint8_t Display_Get_Vehicle_Speed(void) { return display_state.vehicle_speed; }
uint8_t Display_Get_Door_Status(void) { return display_state.door_status; }
uint8_t Display_Get_Drive_Mode(void) { return display_state.drive_mode; }

/* Display state setters */
void Display_Set_Interface_Mode(uint8_t mode) { display_state.interface_mode = mode; }
void Display_Set_Gear_Position(uint8_t gear) { display_state.gear_position = gear; }
void Display_Set_Vehicle_Speed(uint8_t speed) { display_state.vehicle_speed = speed; }
void Display_Set_Door_Status(uint8_t status) { display_state.door_status = status; }
void Display_Set_Drive_Mode(uint8_t mode) { display_state.drive_mode = mode; }

/* Display state increment functions */
void Display_Inc_Interface_Mode(void)
{
    display_state.interface_mode++;
    if(display_state.interface_mode > 6) {
        display_state.interface_mode = 1;
    }
}

void Display_Inc_Gear_Position(void)
{
    display_state.gear_position++;
    if(display_state.gear_position > 4) {
        display_state.gear_position = 4;
    }
}

void Display_Inc_Vehicle_Speed(void)
{
    if(display_state.vehicle_speed < 255) {
        display_state.vehicle_speed += 1;
        if(display_state.vehicle_speed > 255) {
            display_state.vehicle_speed = 255;
        }
    }
}

void Display_Inc_Door_Status(void)
{
    display_state.door_status++;
    if(display_state.door_status > 2) {
        display_state.door_status = 1;
    }
}

void Display_Inc_Drive_Mode(void)
{
    display_state.drive_mode++;
    if(display_state.drive_mode > 4) {
        display_state.drive_mode = 1;
    }
}

/* Display state decrement functions */
void Display_Dec_Interface_Mode(void)
{
    if(display_state.interface_mode > 1) {
        display_state.interface_mode--;
    } else {
        display_state.interface_mode = 6;
    }
}

void Display_Dec_Gear_Position(void)
{
    if(display_state.gear_position > 1) {
        display_state.gear_position--;
    } else {
        display_state.gear_position = 1;
    }
}

void Display_Dec_Vehicle_Speed(void)
{
    if(display_state.vehicle_speed >= 1) {
        display_state.vehicle_speed -= 1;
    } else {
        display_state.vehicle_speed = 0;
    }
}

void Display_Dec_Door_Status(void)
{
    if(display_state.door_status > 1) {
        display_state.door_status--;
    } else {
        display_state.door_status = 2;
    }
}

void Display_Dec_Drive_Mode(void)
{
    if(display_state.drive_mode > 1) {
        display_state.drive_mode--;
    } else {
        display_state.drive_mode = 1;
    }
}
