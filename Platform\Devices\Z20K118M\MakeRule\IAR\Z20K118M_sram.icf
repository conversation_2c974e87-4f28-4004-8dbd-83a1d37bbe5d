/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_0.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__           = 0x1fffc000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_RAM_start__       = 0x1fffc000; 
define symbol __ICFEDIT_region_RAM_end__         = 0x1FFFFFFF;   /* 16KB */

define symbol RAM1_BASE                          = 0x20000000;
define symbol RAM1_SIZE                          = 0x00004000;   /* 16KB */
define symbol RAM1_END                           = RAM1_BASE + RAM1_SIZE - 1;

define symbol __ICFEDIT_size_cstack__            = 0x00000400;
define symbol __ICFEDIT_size_heap__              = 0x00000200;   

/**** End of ICF editor section. ###ICF###*/

define exported symbol __RAM_START             = __ICFEDIT_region_RAM_start__;
define exported symbol __RAM_END               = RAM1_END;

/* memory range */
define memory mem with size = 4G;

define region RAM0_region        = mem:[from __ICFEDIT_region_RAM_start__ to __ICFEDIT_region_RAM_end__];
define region RAM1_region        = mem:[from RAM1_BASE size RAM1_SIZE];

/* definition of RAM blocks */
define block CSTACK    with size = __ICFEDIT_size_cstack__, alignment = 8 { };
define block HEAP      with size = __ICFEDIT_size_heap__, alignment = 8  { };

initialize by copy { readwrite };
do not initialize  { section .noinit };

place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };

place in RAM0_region  { readonly };

place at end of RAM1_region { block HEAP,
                              block CSTACK };

place in RAM1_region  { readwrite };
