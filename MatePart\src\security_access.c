/**************************************************************************************************/
/**
 * @file      : security_access.c
 * @brief     : Security access protocol module
 * @version   : V1.0.0
 * @date      : December-2023
 * <AUTHOR> 
 *
 * @copyright : Copyright (c) 2020-2023 Zhixin Semiconductor Ltd. All rights reserved.
 **************************************************************************************************/

#include "security_access.h"
#include "factory_mode.h"
#include "can.h"
#include "aes128_cmac.h"

/* Security access state variable */
static TestProtocolState_t test_state = Security_IDLE;

/* Security access state variables */
static volatile uint8_t received_seed[16] = {0};  /* Store received 16-byte seed */

/* AES128-CMAC key as specified in requirements */
static const uint8_t aes128_key[16] = {
    0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
    0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
};

/* Security access protocol data */
static uint8_t security_request_seed[64] = {
    0xAA,0xBB, 0xCC, 0xDD, 0xEE,  /* Request seed command (5bytes) */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00         
};

static uint8_t security_send_key[64] = {
    0xEE, 0xDD, 0xCC, 0xBB, 0xAA,  /* Send key command (5 bytes) */
    /* Next 16 bytes will be filled with AES128-CMAC calculated key */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    /* Remaining 43 bytes filled with zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

static uint8_t negative_response[64] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  /* 10 zeros */
    0x00, 0x00, 0x00, 0x00                                        /* 4 zeros, total 64 zeros */
};

/**
 * @brief Initialize security access module
 */
void Security_Access_Init(void)
{
    test_state = Security_IDLE;
    /* Clear received seed buffer */
    for(int i = 0; i < 16; i++) {
        received_seed[i] = 0;
    }
}

/**
 * @brief Calculate security key using AES128-CMAC
 * @param seed_data: Pointer to received 16-byte seed data
 * @param key_output: Output buffer for calculated key (16 bytes)
 */
void Calculate_Security_Key_AES128_CMAC(const uint8_t *seed_data, uint8_t *key_output)
{
    /* Use AES128-CMAC to calculate the security key from the seed */
    AES128_CMAC_Calculate(aes128_key, seed_data, 16, key_output);
}



/**
 * @brief Prepare security key command with calculated key
 */
void Prepare_Security_Key_Command(void)
{
    uint8_t calculated_key[16];

    /* Calculate key using AES128-CMAC */
    Calculate_Security_Key_AES128_CMAC(received_seed, calculated_key);

    /* Fill the calculated key into security_send_key starting at position 5 */
    /* First 5 bytes: EE DD CC BB AA, then 16 bytes of calculated key */
    for(int i = 0; i < 16; i++) {
        security_send_key[5 + i] = calculated_key[i];
    }
}

/**
 * @brief Enter security mode
 */
void Enter_Security_Mode(void)
{
    /* Send request seed command: AA BB CC DD EE */
    CAN_Send_FactoryMode_Msg(0x5F0, security_request_seed);
    test_state = Security_Step1_Sent;
}

/**
 * @brief Handle security access protocol response
 * @param response_data: Pointer to response data
 * @param data_len: Length of response data
 */
void Handle_Security_Access_Response(uint8_t *response_data, uint8_t data_len)
{
    switch(test_state) {
        case Security_Step1_Sent:
            /* Expect: 99 + 16-byte seed + 47 zeros */
            if(response_data[0] == 0x99) {
                /* Store received 16-byte seed (bytes 1-16) */
                for(int i = 0; i < 16; i++) {
                    received_seed[i] = response_data[i + 1];
                }
                /* Prepare and send security key */
                Prepare_Security_Key_Command();
                CAN_Send_Button1_Msg(0x5F0, security_send_key);
                test_state = Security_Step2_Sent;
            }
            break;

        case Security_Step2_Sent:
            /* Handle security access verification result */
            if(response_data[0] == 0xAA) {
                /* AA response: Security access successful, enter factory mode */
                Enter_Factory_Mode();
                test_state = Security_Complete ;
            } else if(response_data[0] == 0xBB) {
                /* BB response: Security access failed, send negative response */
                CAN_Send_Button1_Msg(0x5F0, negative_response);
                test_state = Security_Complete ;
            } else {
                /* Other responses: Send negative response */
                CAN_Send_Button1_Msg(0x5F0, negative_response);
                test_state = Security_Complete ;
            }
            break;

        default:
            break;
    }
}

/**
 * @brief Get current security access state
 * @return Current security access state
 */
TestProtocolState_t Get_Security_Access_State(void)
{
    return test_state;
}

/**
 * @brief Test function to verify AES128-CMAC implementation
 * This function can be called during development to verify the algorithm
 */
void Test_AES128_CMAC_Implementation(void)
{
    /* Test the AES128-CMAC library implementation */
    int test_result = AES128_CMAC_Test();

    /* Test with our specific key and a sample seed */
    uint8_t test_seed[16] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
    };

    uint8_t calculated_key[16];

    /* Calculate key using our implementation */
    Calculate_Security_Key_AES128_CMAC(test_seed, calculated_key);

    /* The calculated_key now contains the AES128-CMAC result */
    /* In a real implementation, you would compare this with expected values */
    /* For debugging, you can examine calculated_key in debugger */

    /* Example: Print first few bytes for verification (if UART available) */
    // printf("AES128-CMAC Test Result: %s\n", test_result == 0 ? "PASS" : "FAIL");
    // printf("Calculated key: %02X %02X %02X %02X...\n",
    //        calculated_key[0], calculated_key[1], calculated_key[2], calculated_key[3]);
}
